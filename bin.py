import os
import cv2
import time
import rospy
from cv_bridge import CvBridge
from ultralytics import YOL<PERSON>
from sensor_msgs.msg import Image
from std_msgs.msg import Int32, String

# 初始化cv_bridge和模型
bridge = CvBridge()
model = YOLO('./bin.pt')  # 仅加载垃圾桶状态检测模型

# 图像接收标志
capture_requested = False

# 垃圾桶状态标签映射（保持不变）
label = {
    '1': '厨余垃圾未投放', '2': '有害垃圾未投放', '3': '其他垃圾未投放', '4': '可回收物未投放',
    '5': '厨余垃圾已投放', '6': '有害垃圾已投放', '7': '其他垃圾已投放', '8': '可回收物已投放'
}

# 创建保存图像的根文件夹
os.makedirs("binimg", exist_ok=True)

def publish_feedback(feedback_status):
    """发布任务完成反馈到ROS话题"""
    feedback_pub = rospy.Publisher('task_feedback', String, queue_size=10)
    rospy.sleep(0.5)  # 确保发布器初始化
    
    feedback_msg = String()
    feedback_msg.data = feedback_status
    rospy.loginfo(f"发布任务反馈：{feedback_status}")
    feedback_pub.publish(feedback_msg)

def image_callback(msg):
    """仅在触发后接收一帧图像"""
    global capture_requested
    
    if capture_requested:
        try:
            # 转换ROS图像为OpenCV格式
            cv_image = bridge.imgmsg_to_cv2(msg, "bgr8")
            rospy.loginfo("成功接收一帧图像")
            
            # 立即重置标志，确保只处理一帧
            capture_requested = False
            
            # 执行检测
            predict(cv_image)
            
        except Exception as e:
            rospy.logerr(f"图像转换错误: {str(e)}")
            
        # 取消订阅避免继续接收图像
        image_sub.unregister()
        rospy.loginfo("已取消图像订阅")

def number_callback(msg):
    """处理数字信号的回调函数"""
    global capture_requested, image_sub
    
    if msg.data == 8:
        rospy.loginfo(f"收到触发信号: {msg.data}，开始请求图像")
        capture_requested = True
        
        # 创建图像订阅器（仅当需要时订阅）
        image_sub = rospy.Subscriber('/camera/color/image_raw', Image, image_callback, queue_size=1)
    else:
        rospy.loginfo(f"收到信号: {msg.data}，忽略非触发信号")

def predict(image):
    """执行垃圾桶状态检测并保存结果"""
    # 生成唯一时间戳
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # 创建本次预测的专用目录
    predict_dir = f"binimg/predict/predict_{timestamp}"
    os.makedirs(predict_dir, exist_ok=True)
    rospy.loginfo(f"创建预测目录: {predict_dir}")
    
    # 文件基础路径（在预测目录内）
    base_filename = f"{predict_dir}/detection_{timestamp}"
    
    # 保存原始图像
    cv2.imwrite(f"{base_filename}.jpg", image)
    rospy.loginfo(f"已保存原始图像: {base_filename}.jpg")
    
    # 执行YOLO检测
    results = model.predict(image, conf=0.25)
    
    # 存储检测结果
    detections = []
    result_data = []
    
    for result in results:
        # 绘制检测结果并保存标注图像
        annotated_frame = result.plot()
        result_img_path = f"{base_filename}_result.jpg"
        cv2.imwrite(result_img_path, annotated_frame)
        rospy.loginfo(f"已保存标注图像: {result_img_path}")
        
        # 解析检测结果 - 主要修改点
        classes = result.boxes.cls.cpu().numpy().tolist()
        for cls in classes:
            # 直接使用完整标签字符串
            detection_str = label.get(str(int(cls) + 1), "未知")
            detections.append(detection_str)
            result_data.append(detection_str)
            rospy.loginfo(f"检测结果: {detection_str}")  # 控制台输出完整格式
    
    # 保存文本结果
    txt_path = f"{base_filename}.txt"
    with open(txt_path, "w") as f:
        f.write("\n".join(result_data))
        rospy.loginfo(f"已保存文本结果: {txt_path}")
    
    # 发布检测结果反馈
    if detections:
        # 直接使用完整的"XX打开/关闭"格式
        publish_feedback(",".join(detections)) 
    else:
        publish_feedback("未检测到垃圾桶")

def subscriber_node():
    """初始化ROS节点并设置订阅器"""
    rospy.init_node('number_subscriber', anonymous=True)
    
    # 创建predict目录（如果不存在）
    os.makedirs("binimg/predict", exist_ok=True)
    
    # 仅订阅数字信号话题（图像话题在需要时动态订阅）
    rospy.Subscriber('number_topic', Int32, number_callback)
    
    rospy.loginfo("节点已启动，等待触发信号...")
    rospy.spin()

if __name__ == "__main__":
    subscriber_node()
