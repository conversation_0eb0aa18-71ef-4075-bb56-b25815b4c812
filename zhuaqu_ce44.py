#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import transforms3d as tfs
import time
import numpy as np  
import tf
from geometry_msgs.msg import TransformStamped, PoseStamped, Pose
import tf2_ros
import math   
import rospy, sys
import moveit_commander
from tf.transformations import euler_from_quaternion, quaternion_from_euler
from std_msgs.msg import Int32
from mirobot_urdf_2.srv import *

# 关节角度限制配置
JOINT_LIMITS = [
    (-3.14, 3.14),   # 关节1
    (-2.09, 2.09),   # 关节2
    (-3.14, 3.14),   # 关节3
    (-3.14, 3.14),   # 关节4
    (-3.14, 3.14),   # 关节5
    (-6.28, 6.28)    # 关节6
]

# 放置位置的关节角度
PLACE_POSITIONS = {
    'tag_1': [1.446808177034463, -0.17311919982642598, 0.912475539777332, 0.0, -0.7393738003005048, -1.4465987194183012],
    'tag_2': [1.9198621444444446, -0.2256885174644979, 0.9803863461530008, 0.0, -0.7546454309949493, -1.920211151699346],
    'tag_3': [2.333662243349525, -0.02789036183663845, 0.7878590439633094, 0.0, -0.7599861341538916, -2.3335400042575754]
}

# 放置位置上方安全点
SAFE_PLACE_POSITIONS = {
    'tag_1': [1.446808177034463, -0.17311919982642598, 0.912475539777332, 0.0, -0.7393738003005048, -1.4465987194183012],
    'tag_2': [1.9198621444444446, -0.2256885174644979, 0.9803863461530008, 0.0, -0.7546454309949493, -1.920211151699346],
    'tag_3': [2.333662243349525, -0.02789036183663845, 0.7878590439633094, 0.0, -0.7599861341538916, -2.3335400042575754]
}

def check_joint_limits(joints):
    """检查关节角度是否在安全范围内"""
    for i, angle in enumerate(joints):
        if not (JOINT_LIMITS[i][0] <= angle <= JOINT_LIMITS[i][1]):
            rospy.logerr("关节%d角度超出限制: %.4f (范围:%.2f - %.2f)", 
                        i+1, angle, JOINT_LIMITS[i][0], JOINT_LIMITS[i][1])
            return False
    return True

def check_tag_exists(tag_id, listener, timeout=0.1):
    """快速检测指定tag是否存在（带超时）"""
    try:
        now = rospy.Time.now()
        listener.waitForTransform("camera_color_optical_frame", tag_id, now, rospy.Duration(timeout))
        return True
    except (tf.Exception, tf.LookupException, tf.ConnectivityException):
        return False

def grasp_object(tag_id, arm, listener, grasp_status_pub):
    """执行单次抓取并放置到指定位置（添加安全高度过渡）"""
    # 设置最大重试次数
    max_retries = 3
    retry_count = 0
    get_pose = False
    posestamped = PoseStamped()
    posestamped.header.frame_id = 'camera_color_optical_frame'
    
    while not rospy.is_shutdown() and not get_pose and retry_count < max_retries:
        try:
            now = rospy.Time.now()
            listener.waitForTransform("camera_color_optical_frame", tag_id, now, rospy.Duration(0.3))
            (trans, rot) = listener.lookupTransform("camera_color_optical_frame", tag_id, now)
            rospy.loginfo("目标标记 %s 位置: x=%.4f, y=%.4f, z=%.4f", tag_id, trans[0], trans[1], trans[2])
            
            posestamped.pose.position.x = trans[0]
            posestamped.pose.position.y = trans[1]
            posestamped.pose.position.z = trans[2]
            posestamped.pose.orientation.x = rot[0]
            posestamped.pose.orientation.y = rot[1]
            posestamped.pose.orientation.z = rot[2]
            posestamped.pose.orientation.w = rot[3]
            m1 = tfs.euler.euler2mat(math.radians(180), math.radians(0), math.radians(0))
            m2 = tfs.quaternions.quat2mat([rot[3], rot[0], rot[1], rot[2]])
            rot2 = tfs.quaternions.mat2quat(np.dot(m2, m1)[0:3, 0:3])

            broadcaster = tf.TransformBroadcaster()
            t2 = TransformStamped()
            t2.header.frame_id = 'camera_color_optical_frame'
            t2.header.stamp = rospy.Time(0)
            t2.child_frame_id = 't2'
            t2.transform.translation = posestamped.pose.position
            t2.transform.rotation.w = rot2[0]
            t2.transform.rotation.x = rot2[1]
            t2.transform.rotation.y = rot2[2]
            t2.transform.rotation.z = rot2[3]
            t2.transform.rotation = posestamped.pose.orientation
            broadcaster.sendTransformMessage(t2)

            get_pose = True

        except (tf.Exception, tf.LookupException, tf.ConnectivityException) as e:
            rospy.logwarn("TF获取 %s 错误: %s，重试中... (%d/%d)", tag_id, str(e), retry_count+1, max_retries)
            retry_count += 1
            time.sleep(0.5)  # 重试前短暂等待
            continue

    # 如果重试后仍无法获取位姿，放弃抓取
    if not get_pose:
        rospy.logerr("无法获取 %s 的位姿，放弃抓取", tag_id)
        return False

    # 转换到base坐标系
    retry_count = 0
    get_pose = False
    while not rospy.is_shutdown() and not get_pose and retry_count < max_retries:
        try:
            now = rospy.Time.now()
            listener.waitForTransform("/base", "/camera_color_optical_frame", now, rospy.Duration(0.5))
            pose_stamped_return = listener.transformPose("/base", posestamped)
            rospy.loginfo("转换到base坐标系的位姿:")
            rospy.loginfo(pose_stamped_return)
            broadcaster.sendTransformMessage(t2)
            get_pose = True
        except (tf.Exception, tf.LookupException, tf.ConnectivityException) as e:
            rospy.logwarn("%s 坐标系转换错误: %s，重试中... (%d/%d)", tag_id, str(e), retry_count+1, max_retries)
            retry_count += 1
            time.sleep(0.5)
            continue

    if not get_pose:
        rospy.logerr("无法将 %s 位姿转换到base坐标系，放弃抓取", tag_id)
        return False

    # 抓取操作
    target_pose = pose_stamped_return
    target_pose.header.stamp = rospy.Time.now()   
    arm.set_start_state_to_current_state()
    rospy.sleep(1)
    
    # 接近物块
    target_pose.pose.position.x = target_pose.pose.position.x - 0.122#0.110
    target_pose.pose.position.y = target_pose.pose.position.y - 0.11#0.078t
    target_pose.pose.position.z = target_pose.pose.position.z - 0.015#0.02
    arm.set_pose_target(target_pose)
    arm.go(wait=True)
    rospy.loginfo("已到达物块 %s 上方位置", tag_id)
    time.sleep(1)
    
    # 最终抓取位置
    target_pose.pose.position.x += 0.025
    arm.set_pose_target(target_pose)
    arm.go(wait=True)
    rospy.loginfo("已到达 %s 抓取位置", tag_id)
    time.sleep(1)
    
    # 开启吸泵
    rospy.wait_for_service("switch_pump_status")
    pump_s = rospy.ServiceProxy("switch_pump_status", mirobotPump)
    response = pump_s(True)
    if hasattr(response, 'success') and response.success:
        rospy.loginfo("抓取 %s 完成，吸泵已开启", tag_id)
    else:
        rospy.logwarn("吸泵开启状态未知，继续执行")
    time.sleep(1)
    
    # 返回Home位
    joint_position = [0, 0, 0, 0, 0, 0]
    arm.set_joint_value_target(joint_position)
    arm.go(wait=True)
    rospy.loginfo("已返回Home位置（携带 %s）", tag_id)
    time.sleep(1)
    
    # 移动到对应的放置位置 - 分两步：先到上方安全点，再到最终放置点
    place_position = PLACE_POSITIONS[tag_id]
    safe_place_position = SAFE_PLACE_POSITIONS[tag_id]  # 使用自定义的上方点
    
    # 检查安全位置关节限制
    if not check_joint_limits(safe_place_position):
        rospy.logerr("%s 安全位置超出关节限制，使用安全位置调整", tag_id)
        # 尝试调整安全位置（在Z轴关节增加偏移）
        safe_place_position = list(place_position)
        safe_place_position[2] += 0.15  # 假设关节3控制Z轴方向
        if not check_joint_limits(safe_place_position):
            rospy.logerr("调整后的安全位置仍超出限制，使用最终放置位置")
            safe_place_position = place_position
    
    # 检查放置位置关节限制
    if not check_joint_limits(place_position):
        rospy.logerr("%s 放置位置超出关节限制，使用安全位置", tag_id)
        place_position = [0, -0.5, 0.5, 0, -0.5, 0]  # 安全位置
        safe_place_position = place_position  # 安全位置也设置为同一个位置
        
    # 1. 先移动到放置点上方安全位置
    arm.set_joint_value_target(safe_place_position)
    arm.go(wait=True)
    rospy.loginfo("已到达 %s 放置点上方安全位置", tag_id)
    time.sleep(1)
    
    # 2. 再移动到最终放置位置
    arm.set_joint_value_target(place_position)
    arm.go(wait=True)
    rospy.loginfo("已到达 %s 最终放置位置", tag_id)
    time.sleep(1)
    
    # 关闭吸泵
    rospy.wait_for_service("switch_pump_status")
    pump_s = rospy.ServiceProxy("switch_pump_status", mirobotPump)
    response = pump_s(False)
    if hasattr(response, 'success') and response.success:
        rospy.loginfo("%s 放置完成，吸泵已关闭", tag_id)
    else:
        rospy.logwarn("吸泵关闭状态未知")
    
    # 返回初始位置
    joint_position = [0, 0, 0, 0, 0, 0]
    arm.set_joint_value_target(joint_position)
    arm.go(wait=True)
    rospy.loginfo("返回初始位置（%s 已放置）", tag_id)
    time.sleep(1)
    
    return True

def grasp_objects(arm, listener, grasp_status_pub):
    """执行抓取任务（优先抓取tag1-3，完成后检测剩余tag）"""
    # 第一轮：按顺序尝试抓取三个tag
    for tag_id in ['tag_1', 'tag_2', 'tag_3']:
        rospy.loginfo("========== 开始处理 %s ==========", tag_id)
        
        # 首先检查tag是否存在
        if not check_tag_exists(tag_id, listener):
            rospy.logwarn("未检测到 %s，跳过抓取", tag_id)
            continue
            
        # 执行抓取
        if not grasp_object(tag_id, arm, listener, grasp_status_pub):
            rospy.logwarn("%s 抓取失败，继续下一个", tag_id)
            
        rospy.loginfo("========== %s 处理完成 ==========\n", tag_id)
        time.sleep(1)
    
    # 第二轮：动态检测并抓取剩余的tag
    rospy.loginfo("========== 开始检测剩余标记 ==========")
    max_attempts = 3  # 最大检测次数
    attempts = 0
    
    while attempts < max_attempts:
        attempts += 1
        tags_detected = False
        
        # 检测所有tag的存在状态
        for tag_id in ['tag_1', 'tag_2', 'tag_3']:
            if check_tag_exists(tag_id, listener):
                rospy.loginfo("检测到剩余标记: %s，开始抓取", tag_id)
                tags_detected = True
                grasp_object(tag_id, arm, listener, grasp_status_pub)
                time.sleep(1)
        
        # 如果本轮未检测到任何tag，立即结束
        if not tags_detected:
            rospy.loginfo("未检测到任何剩余标记，结束抓取任务")
            return True
            
        rospy.loginfo("完成第%d轮剩余抓取，重新检测...", attempts)
        rospy.sleep(1)  # 短暂等待后重新检测
    
    rospy.loginfo("所有物块抓取操作完成！")
    return True

if __name__ =='__main__':
    rospy.init_node("multi_object_grasping")
    
    moveit_commander.roscpp_initialize(sys.argv)
    arm = moveit_commander.MoveGroupCommander('manipulator')
    arm.set_pose_reference_frame('base')
    arm.set_planning_time(5.0)
    arm.set_max_velocity_scaling_factor(0.5)
    arm.set_max_acceleration_scaling_factor(0.3)

    listener = tf.TransformListener()
    
    # 新增发布者（用于发送抓取状态）
    grasp_status_pub = rospy.Publisher("grasp_status", Int32, queue_size=10)
    
    # 新增订阅者（用于接收抓取命令）
    def grasp_command_callback(msg):
        """处理抓取命令的回调函数"""
        if msg.data == 3:  # 3 表示抓取命令
            rospy.loginfo("Received grasp command (3). Starting grasping task...")
            try:
                # 执行抓取任务并发布完成状态
                if grasp_objects(arm, listener, grasp_status_pub):
                    grasp_status_pub.publish(400)  # 400表示所有抓取完成
                    rospy.loginfo("Published grasp completion (400).")
            except Exception as e:
                rospy.logerr("抓取任务异常: %s", str(e))
                grasp_status_pub.publish(400)  # 异常情况下也发布完成状态
    
    rospy.Subscriber("number_topic", Int32, grasp_command_callback)
    
    rospy.loginfo("Grasp node ready. Waiting for command...")
    rospy.spin()
