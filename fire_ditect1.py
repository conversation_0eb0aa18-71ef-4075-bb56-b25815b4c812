import cv2
from ultralytics import YOLO
import rospy
from sensor_msgs.msg import Image
from std_msgs.msg import Int32
import cv_bridge
import numpy as np
import time

# 全局变量
bridge = cv_bridge.CvBridge()
last_frame = None
recognize_enabled = True  # 控制识别状态的标志
last_recognition_time = 0  # 记录上次识别完成的时间
recognition_count = 0  # 记录识别次数，用于切换楼宇名称

def image_callback(msg):
    global last_frame
    try:
        # 将ROS的Image消息转换为OpenCV格式的图像
        last_frame = bridge.imgmsg_to_cv2(msg, "bgr8")
    except cv_bridge.CvBridgeError as e:
        rospy.logerr("图像转换失败: %s", e)

def callback(data):
    global recognize_enabled, last_frame, last_recognition_time, recognition_count
    
    # 检查冷却时间是否结束（距离上次识别完成至少10秒）
    current_time = time.time()
    if current_time - last_recognition_time < 10:
        #rospy.loginfo("识别冷却中，请等待%.1f秒后重试", 10 - (current_time - last_recognition_time))
        return
    
    # 当收到指令1且识别功能已启用时执行识别
    if data.data == 1 and recognize_enabled:
        recognize_enabled = False  # 禁用识别，直到本次完成
        
        # 根据识别次数切换楼宇名称
        if recognition_count % 2 == 0:
            building_name = "美丽商场"
        else:
            building_name = "电子超市"
            
        rospy.loginfo(f"开启楼宇火灾识别！！目标楼宇: {building_name}")
        
        if last_frame is not None:
            save_and_predict(last_frame, building_name)
        else:
            rospy.logwarn("未接收到图像帧，无法进行识别")
            recognize_enabled = True  # 重新启用识别

def save_and_predict(frame, building_name):
    global recognize_enabled, last_recognition_time, recognition_count
    # 保存图像到文件
    output_path = "img/fire_img.jpg"
    cv2.imwrite(output_path, frame)
    rospy.loginfo("图像已保存到 %s", output_path)
    print(f"图像已保存到 {output_path}")
    
    # 调用火灾检测函数
    fire_report = predict(output_path)
    
    # 生成火灾报告
    if fire_report:
        # 对楼层号排序（低层到高层）
        sorted_floors = sorted(fire_report)
        # 格式化为"楼宇名称X层、Y层发生火灾"
        report_str = f"{building_name}{'、'.join(map(str, sorted_floors))}层发生火灾"
        rospy.loginfo(report_str)
        print(report_str)
    else:
        rospy.loginfo(f"{building_name}没有检测到火灾")
        print(f"{building_name}没有检测到火灾")
    
    # 记录识别完成时间和增加识别次数
    last_recognition_time = time.time()
    recognition_count += 1
    
    # 显示下次识别将切换的楼宇名称
    next_building = "电子超市" if building_name == "美丽商场" else "美丽商场"
    print(f"识别完成，等待10秒冷却时间...下次识别目标: {next_building}")
    
    # 完成识别后重新启用识别功能
    recognize_enabled = True

def predict(output_path):
    model = YOLO("/home/<USER>/robocom_ws/src/firebest.pt")
    results = model(source=output_path, conf=0.65, save=True)
    
    # 存储有火灾的楼层集合
    fire_floors = set()

    for result in results:
        boxes = result.boxes
        if boxes is None or len(boxes) == 0:
            rospy.loginfo("未检测到任何目标")
            return fire_floors

        # 提取所有楼层框
        floor_boxes = []
        # 提取所有火焰框
        fire_boxes = []
        
        # 遍历所有检测框，分类存储
        for box, cls in zip(boxes.xyxy, boxes.cls):
            # 类别0: 火焰
            if cls == 1:
                fire_boxes.append(box)
            # 类别1: 楼层
            elif cls == 0:
                floor_boxes.append(box)
                
        # 获取火焰数量和楼层数量
        fire_count = len(fire_boxes)
        floor_count = len(floor_boxes)

        # 输出检测到的总数量
        print(f'检测到火灾数量: {fire_count}, 检测到楼层数量: {floor_count}')

        # 如果没有检测到楼层或火焰，直接返回
        if not floor_boxes or not fire_boxes:
            return fire_floors
            
        # 计算所有楼层框的中心点y坐标
        floor_centers = []
        for box in floor_boxes:
            y1 = box[1].item()
            y2 = box[3].item()
            center_y = (y1 + y2) / 2.0
            floor_centers.append(center_y)
            
        # 按中心点y坐标从大到小排序（物理低楼层→高楼层）
        sorted_floor_indices = sorted(range(len(floor_centers)), 
                                      key=lambda i: floor_centers[i], 
                                      reverse=True)
        
        # 创建楼层映射表：图像下方为1楼，向上依次递增
        floor_mapping = {idx: floor_num + 1 
                         for floor_num, idx in enumerate(sorted_floor_indices)}

        # 匹配每个火焰框
        for fire_box in fire_boxes:
            # 计算火焰框中心点y坐标
            y1 = fire_box[1].item()
            y2 = fire_box[3].item()
            fire_center = (y1 + y2) / 2.0
            
            # 寻找最近的楼层中心点
            min_diff = float('inf')
            matched_floor_idx = -1
            
            for idx in range(len(floor_centers)):
                diff = abs(fire_center - floor_centers[idx])
                if diff < min_diff:
                    min_diff = diff
                    matched_floor_idx = idx  # 原始楼层索引
                    
            # 使用映射表获取实际楼层号
            if matched_floor_idx in floor_mapping:
                floor_number = floor_mapping[matched_floor_idx]
                # 添加到有火灾的楼层集合
                fire_floors.add(floor_number)
    
    return fire_floors

def subscriber_node():
    rospy.init_node('number_subscriber', anonymous=True)

    # 订阅摄像头图像话题
    rospy.Subscriber('/camera/color/image_raw', Image, image_callback)

    # 订阅任务完成信号
    rospy.Subscriber('number_topic', Int32, callback)

    rospy.loginfo("火灾识别节点已启动，等待识别指令...首次识别目标: 美丽商场")
    rospy.spin()

if __name__ == "__main__":
    subscriber_node()
