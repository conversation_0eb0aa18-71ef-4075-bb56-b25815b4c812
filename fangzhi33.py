#!/usr/bin/env python2
# -*- coding: UTF-8 -*-
import rospy
import sys
import time
import moveit_commander
from std_msgs.msg import Int32, String
from mirobot_urdf_2.srv import mirobotPump, mirobotPumpResponse

# ===== 全局变量 =====
place_command_received = False  # 放置指令标志位

# ===== 关节位置配置 =====
HOME_JOINTS = [0, 0, 0, 0, 0, 0]
GRAB_PREP_JOINTS = [2.2863637042482163, -0.018011796624582212, 0.9771051320089128, 0.0, -0.9589188047091505, -2.2863288168385316]

#[2.4028993705044557, 0.2033134040261438, 0.8890357944218658, -0.00546288035320345, -1.1255104470123292, -2.454107430019311]
PLACE_SAFE_JOINTS = [2.355583520856357, -0.10138617478520498, 0.8273907401939986, 0.0, -0.7262838311338383, -2.3553393089885626]  # 安全点
PLACE_JOINTS = [0.1753706861078432, 0.8620006484980689, -0.9888336932495542, -0.8123984085141119, -1.134446550739602, -0.8202872907396019]

# ===== 关节角度限制配置 =====
JOINT_LIMITS = [
    (-3.14, 3.14),   # 关节1
    (-2.09, 2.09),   # 关节2
    (-3.14, 3.14),   # 关节3
    (-3.14, 3.14),   # 关节4
    (-3.14, 3.14),   # 关节5
    (-6.28, 6.28)    # 关节6
]

def command_callback(msg):
    """指令回调函数 - 接收Int32类型指令"""
    global place_command_received
    if msg.data == 6:  # 放置指令
        rospy.loginfo("接收到放置指令(6)")
        place_command_received = True

def check_joint_limits(joints):
    """检查关节角度安全范围"""
    for i, angle in enumerate(joints):
        if not (JOINT_LIMITS[i][0] <= angle <= JOINT_LIMITS[i][1]):
            rospy.logerr("关节%d角度超出限制: %.4f (范围:%.2f - %.2f)", 
                        i+1, angle, JOINT_LIMITS[i][0], JOINT_LIMITS[i][1])
            return False
    return True

def initialize_moveit():
    """初始化MoveIt环境"""
    moveit_commander.roscpp_initialize(sys.argv)
    arm = moveit_commander.MoveGroupCommander('manipulator')
    
    # 运动参数配置
    arm.set_pose_reference_frame('base')
    arm.set_planning_time(10.0)
    arm.set_max_velocity_scaling_factor(0.3)
    arm.set_max_acceleration_scaling_factor(0.2)
    arm.allow_replanning(True)  # 允许重新规划
    
    rospy.loginfo("MoveIt! 初始化完成")
    return arm

def pump_control(enable):
    """吸泵控制服务调用"""
    try:
        rospy.wait_for_service("switch_pump_status", timeout=2.0)
        pump_srv = rospy.ServiceProxy("switch_pump_status", mirobotPump)
        resp = pump_srv(enable)
        
        # 兼容不同响应字段
        if hasattr(resp, 'success') and resp.success:
            rospy.loginfo("吸泵已 %s", "开启" if enable else "关闭")
            return True
        elif hasattr(resp, 'result') and resp.result:
            rospy.loginfo("吸泵已 %s (使用result字段)", "开启" if enable else "关闭")
            return True
        else:
            rospy.logwarn("吸泵控制状态未知，但指令已发送")
            return True
    except (rospy.ServiceException, rospy.ROSException) as e:
        rospy.logerr("吸泵服务异常: %s", str(e))
        return False

def move_to_joints(arm, target_joints, position_name="目标位置", max_attempts=3):
    """安全关节空间运动"""
    if not check_joint_limits(target_joints):
        rospy.logerr("%s 关节位置超出安全限制，取消移动", position_name)
        return False
    
    attempts = 0
    while attempts < max_attempts:
        arm.set_joint_value_target(target_joints)
        result = arm.go(wait=True)
        if result:
            rospy.loginfo("已到达 %s", position_name)
            return True
        else:
            attempts += 1
            rospy.logwarn("移动到 %s 失败 (尝试 %d/%d)", position_name, attempts, max_attempts)
            rospy.sleep(1.0)
    rospy.logerr("无法到达 %s，放弃尝试", position_name)
    return False

def grasp_procedure(arm):
    """完整抓取流程"""
    # 1. 移动到抓取预备位
    if not move_to_joints(arm, GRAB_PREP_JOINTS, "抓取预备位"):
        return False
    
    # 2. 执行抓取操作
    grab_joints = [GRAB_PREP_JOINTS[0], GRAB_PREP_JOINTS[1] + 0.1] + GRAB_PREP_JOINTS[2:]  # Y轴微调
    if not move_to_joints(arm, grab_joints, "抓取位置"):
        return False
    
    # 3. 开启吸泵并确保稳定
    if not pump_control(True):
        rospy.logwarn("抓取物体失败，吸泵未开启")
        return False
    time.sleep(1.0)  # 确保抓取稳定
    
    # 4. 返回预备位
    if not move_to_joints(arm, GRAB_PREP_JOINTS, "抓取预备位"):
        return False
    
    return True

def place_procedure(arm):
    """完整放置流程（增加安全点）"""
    # 1. 先移动到安全点
    if not move_to_joints(arm, PLACE_SAFE_JOINTS, "放置安全点"):
        return False
    
    # 2. 从安全点移动到精确放置点
    if not move_to_joints(arm, PLACE_JOINTS, "精确放置位"):
        return False
    
    # 3. 关闭吸泵并释放物体
    if not pump_control(False):
        rospy.logwarn("放置物体失败，吸泵未关闭")
        return False
    time.sleep(0.5)  # 确保物体释放
    
    
    # 5. 返回Home位
    if not move_to_joints(arm, HOME_JOINTS, "Home位"):
        return False
    
    return True

def execute_full_cycle(arm, task_feedback_pub, grasp_status_pub):
    """执行完整抓取-放置周期并发布反馈 (修改后逻辑)"""
    global place_command_received
    
    rospy.loginfo("===== 等待指令6启动任务 =====")
    # 1. 等待指令6 (放置指令作为启动信号)
    place_command_received = False  # 重置标志位
    while not place_command_received and not rospy.is_shutdown():
        rospy.sleep(0.1)  # 避免CPU空转
    
    if rospy.is_shutdown():
        return False
    
    rospy.loginfo("===== 收到启动指令(6)，开始抓取流程 =====")
    # 2. 执行抓取操作
    if grasp_procedure(arm):
        rospy.loginfo("抓取成功! 立即执行放置流程...")
        
        # 3. 执行放置操作 (紧接抓取之后，不再等待新指令)
        rospy.loginfo("===== 开始放置流程 =====")
        if place_procedure(arm):
            rospy.loginfo("放置成功!")
            
            # ===== 发布任务完成反馈 =====
            feedback_msg = String()
            feedback_msg.data = "ok"
            task_feedback_pub.publish(feedback_msg)
            rospy.loginfo("已发布任务完成反馈: ok")
            
            status_msg = Int32()
            status_msg.data = 400
            grasp_status_pub.publish(status_msg)
            rospy.loginfo("已发布抓取状态: 400 (放置完成)")
            return True
        else:
            rospy.logerr("放置失败")
    else:
        rospy.logerr("抓取失败")
    return False

def main():
    global place_command_received
    rospy.init_node('grasp_place_node', anonymous=True)
    
    # ===== 新增通信接口 =====
    rospy.Subscriber("number_topic", Int32, command_callback)
    task_feedback_pub = rospy.Publisher('task_feedback', String, queue_size=10)
    grasp_status_pub = rospy.Publisher('grasp_status', Int32, queue_size=10)
    rospy.loginfo("通信接口初始化完成")
    
    # 初始化机械臂
    arm = initialize_moveit()
    move_to_joints(arm, HOME_JOINTS, "初始Home位")
    
    try:
        # 主循环 (修改后)
        while not rospy.is_shutdown():
            if execute_full_cycle(arm, task_feedback_pub, grasp_status_pub):
                rospy.loginfo("===== 任务完成，等待新指令6 =====")
            else:
                rospy.logerr("任务失败，等待新指令6")
                rospy.sleep(0.5)  # 避免频繁尝试
                
    except (rospy.ROSInterruptException, Exception) as e:
        rospy.logerr("程序异常: %s", str(e))
    finally:
        pump_control(False)
        move_to_joints(arm, HOME_JOINTS, "安全Home位")
        moveit_commander.roscpp_shutdown()
        rospy.loginfo("节点已安全关闭")

if __name__ == '__main__':
    main()
