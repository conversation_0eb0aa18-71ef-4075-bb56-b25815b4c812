# 平安城市-D 智能机器人系统

## 📋 项目概述

平安城市-D是一个基于ROS（Robot Operating System）的综合性智能机器人系统，集成了计算机视觉、机械臂控制、自主导航等多个功能模块，主要用于城市安全监控和自动化任务执行。

## 🏗️ 系统架构

```
平安城市-D系统
├── 机械臂控制模块
├── 自主导航模块  
├── 计算机视觉模块
└── AI模型库
```

## 📁 文件功能详解

### 🤖 机械臂控制模块

#### `fangzhi11.py` - 位置1机械臂控制
- **功能**: 控制机械臂在第一个工作区域执行抓取放置任务
- **触发指令**: 监听指令4
- **特点**: 
  - 使用MoveIt!进行路径规划
  - 集成吸泵控制服务
  - 包含安全关节限制检查
  - 支持抓取-放置完整流程

```python
# 关节位置配置示例
HOME_JOINTS = [0, 0, 0, 0, 0, 0]
GRAB_PREP_JOINTS = [1.4829713740178256, -0.07333873638120174, ...]
```

#### `fangzhi22.py` - 位置2机械臂控制
- **功能**: 控制机械臂在第二个工作区域执行任务
- **触发指令**: 监听指令5
- **特点**: 与fangzhi11.py功能相同，但配置不同的关节位置参数

#### `fangzhi33.py` - 位置3机械臂控制
- **功能**: 控制机械臂在第三个工作区域执行任务
- **触发指令**: 监听指令6
- **特点**: 针对第三个工作区域优化的关节位置配置

#### `zhuaqu_ce44.py` - 多目标视觉抓取系统
- **功能**: 基于AprilTag的精确多目标抓取
- **支持目标**: tag_1, tag_2, tag_3
- **特点**:
  - 使用TF坐标变换进行精确定位
  - 支持多轮抓取确保完整性
  - 包含放置位置预设配置
  - 具备错误恢复机制

```python
# 放置位置配置
PLACE_POSITIONS = {
    'tag_1': [1.446808177034463, -0.17311919982642598, ...],
    'tag_2': [1.9198621444444446, -0.2256885174644979, ...],
    'tag_3': [2.333662243349525, -0.02789036183663845, ...]
}
```

### 🚗 自主导航模块

#### `tracking13.py` - 智能巡线导航系统
- **功能**: 基于视觉的自主导航和任务调度
- **核心特性**:
  - **黑线跟踪**: 使用HSV颜色空间和边缘检测
  - **PID控制**: 实现平滑转向控制
  - **里程计导航**: 基于距离的精确任务点触发
  - **AprilTag检测**: tag_4和tag_5的识别与响应
  - **冲坡模式**: 特殊地形处理（25-28米区间）
  - **弯道计数**: 左右弯道统计功能

```python
# 任务点配置（距离触发）
self.task_points = [
    (4.5, 1),   # 4.5米处发布任务1
    (9.4, 2),   # 9.4米处发布任务2
    (12, 2),    # 12米处发布任务2
    (16.1, 1),  # 16.1米处发布任务1
    (18.4, 2),  # 18.4米处发布任务2
    (20.65, 8), # 20.65米处发布任务8
    (23.25, 2), # 23.25米处发布任务2
    (23.9, 10), # 23.9米处开始冲坡
    (26.0, 11), # 26米处结束冲坡
    (28, 3)     # 28米处发布任务3
]
```

### 👁️ 计算机视觉模块

#### `bin.py` - 垃圾桶状态检测系统
- **功能**: 智能垃圾分类状态监测
- **检测类型**: 
  - 厨余垃圾（未投放/已投放）
  - 有害垃圾（未投放/已投放）
  - 其他垃圾（未投放/已投放）
  - 可回收物（未投放/已投放）
- **触发条件**: 接收指令8时启动检测
- **输出**: 
  - 保存原始图像和标注图像
  - 生成文本检测报告
  - 通过ROS话题发布结果

```python
# 垃圾桶状态标签映射
label = {
    '1': '厨余垃圾未投放', '2': '有害垃圾未投放', 
    '3': '其他垃圾未投放', '4': '可回收物未投放',
    '5': '厨余垃圾已投放', '6': '有害垃圾已投放', 
    '7': '其他垃圾已投放', '8': '可回收物已投放'
}
```

#### `fire_ditect1.py` - 火灾检测预警系统
- **功能**: 建筑物火灾智能检测与定位
- **检测目标**: 
  - 火焰识别
  - 楼层定位
  - 建筑物识别
- **支持建筑**: 
  - 美丽商场
  - 电子超市（轮换检测）
- **特点**:
  - 10秒冷却时间防止重复检测
  - 精确的楼层匹配算法
  - 详细的火灾报告生成

```python
# 建筑物轮换逻辑
if recognition_count % 2 == 0:
    building_name = "美丽商场"
else:
    building_name = "电子超市"
```

#### `people1.py` - 人员识别统计系统
- **功能**: 智能人员分类与计数
- **识别类型**:
  - 职业人员（类别0）
  - 普通人员（类别1）
- **特点**:
  - 任务状态管理
  - 支持重置功能
  - 唯一任务ID标识
  - 自动图像保存

### 🧠 AI模型库

#### `best.pt`
- **用途**: 人员检测YOLO模型
- **应用**: people1.py中的人员分类识别
- **性能**: 置信度阈值0.35，支持CPU推理

#### `bin.pt`
- **用途**: 垃圾桶状态检测YOLO模型
- **应用**: bin.py中的垃圾桶状态识别
- **性能**: 置信度阈值0.25

#### `firebest.pt`
- **用途**: 火灾检测YOLO模型
- **应用**: fire_ditect1.py中的火灾和楼层检测
- **性能**: 置信度阈值0.65

#### `traffic(1).zip`
- **内容**: 交通相关模型文件压缩包
- **可能用途**: 交通标志识别、车辆检测等扩展功能

## 🔄 系统工作流程

### 1. 系统启动
```
启动tracking13.py → 初始化导航系统 → 开始巡线
```

### 2. 任务执行流程
```
距离/Tag触发 → 发布任务指令 → 调用对应模块 → 执行检测/抓取 → 返回结果
```

### 3. 通信机制
- **话题通信**: 
  - `number_topic`: 任务指令发布
  - `task_feedback`: 任务完成反馈
  - `grasp_status`: 抓取状态反馈
  - `/camera/color/image_raw`: 图像数据流

## ⚙️ 技术特点

### 🔧 模块化设计
- 每个功能独立封装
- 便于维护和扩展
- 支持并行开发

### 🌐 ROS生态集成
- 标准ROS话题和服务
- TF坐标变换系统
- MoveIt!运动规划

### 🤖 深度学习集成
- 多个专用YOLO模型
- 实时推理能力
- 高精度检测

### 🛡️ 安全机制
- 关节限制检查
- 错误处理和恢复
- 冷却时间保护

## 📊 应用场景

- **城市安全监控**: 火灾预警、人员统计
- **环保管理**: 垃圾分类状态监测
- **自动化作业**: 物品抓取、分拣作业
- **智能巡检**: 自主导航巡检任务

## 🚀 扩展性

系统采用模块化设计，支持：
- 新增检测模型
- 扩展机械臂工作区域
- 增加导航路径点
- 集成更多传感器

---

*该系统为平安城市建设提供了完整的智能化解决方案，具备高度的可靠性和扩展性。*
