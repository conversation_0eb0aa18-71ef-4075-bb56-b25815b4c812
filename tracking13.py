#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
from sensor_msgs.msg import Image
from std_msgs.msg import Int32
from nav_msgs.msg import Odometry

import cv2
import cv_bridge
import numpy as np
from geometry_msgs.msg import Twist, PoseStamped, Point
import time
import tf
import math

class Follower:
    def __init__(self):
        self.bridge = cv_bridge.CvBridge()
        self.image_sub = rospy.Subscriber("/usb_cam/image_raw", Image, self.image_callback)
        self.cmd_vel_pub = rospy.Publisher("/cmd_vel", Twist, queue_size=1)
        self.task_complete_pub = rospy.Publisher("number_topic", Int32, queue_size=10)
        self.twist = Twist()

        # TF监听器
        self.tf_listener = tf.TransformListener()
        
        # 里程计相关变量
        self.odom_sub = rospy.Subscriber("/odom", Odometry, self.odom_callback)
        self.current_pose = None
        self.start_pose = None
        self.last_pose = None
        self.distance_traveled = 0.0
        self.odom_initialized = False
        
        # Tag_4状态
        self.tag_4_detected = False
        self.tag_4_detected_time = 0
        self.tag_4_completed = False
        
        # Tag_5状态
        self.tag_5_detected = False
        self.tag_5_detected_time = 0
        self.tag_5_trigger_count = 0
        self.tag_5_completed = False
        
        self.grasp_triggered = False
        
        # 订阅抓取完成消息
        self.grasp_status_sub = rospy.Subscriber("grasp_status", Int32, self.grasp_status_callback)
        self.grasp_completed = False
        
        # Tag禁用期状态
        self.tag_disable_duration = 10
        self.tag_disable_until = 0
        self.tag_5_disable_until = 1

        # 任务点配置（基于距离）
        self.task_points = [
            (4.5, 1),   # 发布任务1
            (9.4, 2),   # 发布任务2
            (12, 2),
            (16.1, 1),   # 发布任务1
            (18.4, 2),   # 发布任务2
            (20.65, 8),   # 发布任务8
            (23.25, 2),   # 发布任务2
            (23.9, 10),  # 新增：开始冲坡（任务10）
            (26.0, 11),  # 新增：结束冲坡（任务11）
            (28, 3)   # 发布任务3
        ]
        self.task_triggered = [False] * len(self.task_points)
        
        # 冲坡状态
        self.ramp_mode = False

        # PID参数
        self.Kp = 0.034
        self.Ki = 0.0
        self.Kd = 0.4
        self.Error = 0.0
        self.LastError = 0.0
        self.LastLastError = 0.0
        self.PIDOutput = 0.0

        # 巡线状态
        self.search_count = 0
        self.line_following_done = False
        self.paused = False
        self.last_valid_cx = None
        self.estimated_track_width_left = 0.5 * 640
        self.estimated_track_width_right = 0.5 * 640
        self.estimated_track_width_l = 0.4 * 640
        self.estimated_track_width_r = 0.6 * 640

        # 弯道计数
        self.left_curve_count = 0
        self.right_curve_count = 0
        self.last_state = 'straight'
        self.curve_threshold = 30

        # 定义黑色的HSV范围
        self.lower_black = np.array([0, 0, 0])
        self.upper_black = np.array([120, 115, 115])
        
        # 暂停标志
        self.paused_at_6_2 = False  # 确保只暂停一次[1,2](@ref)
        self.paused_at_6_6 = False
        self.paused_at_7_1 = False

    def resume_from_pause(self, event):
        """恢复运行的回调函数"""
        if self.paused:  # 确保当前是暂停状态
            self.paused = False
            rospy.loginfo("6.2米暂停结束，恢复运行")

    def odom_callback(self, msg):
        """里程计回调函数 - 修改为累积路径长度计算"""
        current_pose = msg.pose.pose.position
        
        # 初始化起点
        if not self.odom_initialized:
            self.start_pose = current_pose
            self.last_pose = current_pose
            self.current_pose = current_pose
            self.distance_traveled = 0.0
            self.odom_initialized = True
            rospy.loginfo("里程计已初始化，起点位置: (%.2f, %.2f)", 
                          self.start_pose.x, self.start_pose.y)
            return
            
        # 计算与上一次位置的距离（路径累积）
        dx = current_pose.x - self.last_pose.x
        dy = current_pose.y - self.last_pose.y
        segment_distance = math.sqrt(dx*dx + dy*dy)
        
        # 累加行驶距离（确保距离值只增不减）
        self.distance_traveled += segment_distance
        
        # 更新位置记录
        self.last_pose = current_pose
        self.current_pose = current_pose
        
        # 检查任务点
        if not self.paused and not self.line_following_done:
            for i, (distance, task_id) in enumerate(self.task_points):
                if not self.task_triggered[i]:
                    # 判断当前距离是否在任务点的触发范围内
                    if distance - 0.1 <= self.distance_traveled <= distance + 0.1:
                        rospy.loginfo("到达任务点: 距离%.1f米，发布任务指令%d", 
                                     distance, task_id)
                        self.task_complete_pub.publish(task_id)
                        self.task_triggered[i] = True
                        
                        # 处理特殊任务
                        if task_id == 10:  # 开始冲坡
                            self.ramp_mode = True
                            rospy.loginfo("进入冲坡模式 (25-28m)")
                        elif task_id == 11:  # 结束冲坡
                            self.ramp_mode = False
                            rospy.loginfo("退出冲坡模式")
                        
                        # 如果是最后一个任务点，则标记巡线完成
                        if task_id == 3:
                            self.line_following_done = True
                            rospy.loginfo("巡线任务完成")
                        
                       
    def check_tag(self, tag_name):
        """检查是否检测到指定tag（增加禁用期判断）"""
        if tag_name == "tag_4":
            disable_until = self.tag_disable_until
        else:
            disable_until = self.tag_5_disable_until
            
        if rospy.get_time() < disable_until:
            if tag_name == "tag_4":
                self.tag_4_detected = False
                self.tag_4_detected_time = 0
            else:
                self.tag_5_detected = False
                self.tag_5_detected_time = 0
            return False
            
        try:
            (trans, rot) = self.tf_listener.lookupTransform(
                "camera_color_optical_frame", tag_name, rospy.Time(0))
            
            if tag_name == "tag_4":
                if not self.tag_4_detected:
                    self.tag_4_detected = True
                    self.tag_4_detected_time = rospy.get_time()
                    rospy.loginfo("首次检测到tag_4，开始计时")
                return True
            else:
                if not self.tag_5_detected:
                    self.tag_5_detected = True
                    self.tag_5_detected_time = rospy.get_time()
                    rospy.loginfo("首次检测到tag_5，开始计时")
                return True
                
        except (tf.LookupException, tf.ConnectivityException, tf.ExtrapolationException):
            if tag_name == "tag_4":
                self.tag_4_detected = False
                self.tag_4_detected_time = 0
            else:
                self.tag_5_detected = False
                self.tag_5_detected_time = 0
            return False

    def grasp_status_callback(self, msg):
        """处理抓取完成消息的回调函数"""
        if msg.data in [300, 400]:
            self.grasp_completed = True
            self.distance_traveled += 0.11
            self.paused = False
            self.grasp_triggered = False
            
            # 重置所有标签检测状态
            self.tag_4_detected = False
            self.tag_4_detected_time = 0
            self.tag_5_detected = False
            self.tag_5_detected_time = 0
            
            # 启动Tag禁用倒计时
            self.tag_disable_until = rospy.get_time() + self.tag_disable_duration
            rospy.loginfo("标签检测禁用 %.1f 秒", self.tag_disable_duration)

    def calculate_pid_output(self, cx, w):
        self.LastLastError = self.LastError
        self.LastError = self.Error
        self.Error = w // 2 - cx
        IncrementalValue = self.Kp * (self.Error - self.LastError) + self.Ki * self.Error + self.Kd * (
                    self.Error - 2 * self.LastError + self.LastLastError)
        self.PIDOutput += IncrementalValue
        return self.PIDOutput

    def detect_edge_centroid(self, region, is_right=False):
        """检测区域中的黑白边缘质心"""
        grad_y = cv2.Sobel(region, cv2.CV_64F, 0, 1, ksize=3)
        grad_y = np.absolute(grad_y)
        grad_y = np.uint8(255 * grad_y / np.max(grad_y))
        
        _, edge_mask = cv2.threshold(grad_y, 50, 255, cv2.THRESH_BINARY)
        
        if is_right:
            left_edge_mask = np.zeros_like(edge_mask)
            cols = edge_mask.shape[1]
            for r in range(edge_mask.shape[0]):
                for c in range(cols):
                    if edge_mask[r, c] > 0:
                        left_edge_mask[r, c] = 255
                        break
        
            M = cv2.moments(left_edge_mask)
            if M['m00'] > 0:
                cx = int(M['m10'] / M['m00'])
                cy = int(M['m01'] / M['m00'])
                return cx, cy
            return None, None
        else:
            right_edge_mask = np.zeros_like(edge_mask)
            cols = edge_mask.shape[1]
            for r in range(edge_mask.shape[0]):
                for c in range(cols-1, -1, -1):
                    if edge_mask[r, c] > 0:
                        right_edge_mask[r, c] = 255
                        break
            
            M = cv2.moments(right_edge_mask)
            if M['m00'] > 0:
                cx = int(M['m10'] / M['m00'])
                cy = int(M['m01'] / M['m00'])
                return cx, cy
            return None, None

    def update_curve_count(self, error_value):
        """更新弯道计数，只计数从直道进入弯道的变化"""
        current_state = 'straight'
        
        if error_value > self.curve_threshold:
            current_state = 'left_curve'
        elif error_value < -self.curve_threshold:
            current_state = 'right_curve'
        
        if self.last_state == 'straight':
            if current_state == 'left_curve':
                self.left_curve_count += 1
                rospy.loginfo("进入左弯道。左弯计数: %d", self.left_curve_count)
            elif current_state == 'right_curve':
                self.right_curve_count += 1
                rospy.loginfo("进入右弯道。右弯计数: %d", self.right_curve_count)
        
        self.last_state = current_state

    def image_callback(self, msg):
        if self.paused and self.grasp_completed:
            self.paused = False
            self.grasp_completed = False
            rospy.loginfo("自动恢复运行状态")
            
        if self.line_following_done or self.paused:
            return

        # 检查tag_4
        if not self.tag_4_completed and self.check_tag("tag_4"):
            current_time = rospy.get_time()
            
            if current_time - self.tag_4_detected_time >= 0.0:
                rospy.loginfo("tag_4持续检测1秒! 停止运行...")
                self.twist.linear.x = 0
                self.twist.angular.z = 0
                self.cmd_vel_pub.publish(self.twist)
                self.paused = True
                
                if not self.grasp_triggered:
                    self.task_complete_pub.publish(3)
                    self.grasp_triggered = True
                    rospy.loginfo("抓取指令已发布(仅一次)")
                
                self.tag_4_completed = True
                self.tag_4_detected = False
                self.tag_4_detected_time = 0
                return
                
        # 检查tag_5
        if not self.tag_5_completed and self.check_tag("tag_5") and self.tag_5_trigger_count < 4:
            current_time = rospy.get_time()
            
            if not self.tag_5_detected:
                self.tag_5_detected = True
                self.tag_5_detected_time = current_time
                rospy.loginfo("tag_5检测开始计时")
            
            if current_time - self.tag_5_detected_time >= 1.0:
                rospy.loginfo("tag_5持续检测1秒! 触发第%d次任务", self.tag_5_trigger_count+1)
                self.twist.linear.x = 0
                self.twist.angular.z = 0
                self.cmd_vel_pub.publish(self.twist)
                self.paused = True
                
                if self.tag_5_trigger_count == 0:
                    self.task_complete_pub.publish(4)
                    rospy.loginfo("444")
                elif self.tag_5_trigger_count == 1:
                    self.task_complete_pub.publish(5)
                    rospy.loginfo("555")
                elif self.tag_5_trigger_count == 2:
                    self.task_complete_pub.publish(6)
                    rospy.loginfo("666")
                
                self.tag_5_trigger_count += 1
                
                if self.tag_5_trigger_count >= 3:
                    self.tag_5_completed = True
                    rospy.loginfo("tag_5已达到最大触发次数(3次)，停止检测")
                
                self.tag_5_detected = False
                self.tag_5_disable_until = rospy.get_time() + self.tag_disable_duration
                rospy.loginfo("tag_5检测禁用 %.1f 秒", self.tag_disable_duration)
                return

        image = self.bridge.imgmsg_to_cv2(msg, desired_encoding='bgr8')
        h, w = image.shape[:2]
        
        # 显示行驶距离
        if self.odom_initialized:
            cv2.putText(image, f"Distance: {self.distance_traveled:.2f}m", (w-300, 30), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            # 显示冲坡状态
            if self.ramp_mode:
                cv2.putText(image, "RAMP MODE: ON", (w-300, 60), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # 图像处理
        blurred = cv2.GaussianBlur(image, (5, 5), 0)
        hsv = cv2.cvtColor(blurred, cv2.COLOR_BGR2HSV)
        mask = cv2.inRange(hsv, self.lower_black, self.upper_black)
        kernel = np.ones((3, 3), np.uint8)
        cleaned_mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        binary = cleaned_mask
        
        search_top = int(3 * h / 4)
        search_bot = min(search_top + 20, h)
        mid_width = w // 2
        
        # 左右区域检测
        left_region = binary[search_top:search_bot, 0:mid_width]
        right_region = binary[search_top:search_bot, mid_width:w]
        
        # 检测左侧边缘质心
        left_cx, left_cy = self.detect_edge_centroid(left_region, is_right=False)
        left_detected = left_cx is not None
        if left_detected:
            left_cx_global = left_cx
            left_cy_global = search_top + left_cy
            cv2.circle(image, (left_cx_global, left_cy_global), 10, (0, 255, 0), -1)

        # 检测右侧边缘质心
        right_cx, right_cy = self.detect_edge_centroid(right_region, is_right=True)
        right_detected = right_cx is not None
        if right_detected:
            right_cx_global = mid_width + right_cx
            right_cy_global = search_top + right_cy
            cv2.circle(image, (right_cx_global, right_cy_global), 10, (255, 0, 0), -1)

        # 计算赛道中心点
        if left_detected and right_detected:
            cx = (left_cx_global + right_cx_global) // 2 - 25
            self.last_valid_cx = cx
            status = "Both"
        elif left_detected:
            cx = int(left_cx_global + self.estimated_track_width_left) - 50
            if self.last_valid_cx is not None:
                cx = int(0.7 * cx + 0.3 * self.last_valid_cx) 
            self.last_valid_cx = cx
            status = "Left"
        elif right_detected:
            cx = int(right_cx_global - self.estimated_track_width_right) + 20
            if self.last_valid_cx is not None:
                cx = int(0.7 * cx + 0.3 * self.last_valid_cx) 
            self.last_valid_cx = cx
            status = "Right"
        else:
            if self.last_valid_cx is not None:
                cx = self.last_valid_cx
            else:
                cx = w // 2
            status = "None"

        cy = (search_top + search_bot) // 2
        
        # 冲坡模式处理（25m-28m）
        if self.ramp_mode:
            # 冲坡模式：直行，不进行PID控制
            self.twist.linear.x = 0.3  # 冲坡速度
            self.twist.angular.z = 0   # 保持直行方向
            self.cmd_vel_pub.publish(self.twist)
            # 在图像上显示冲坡状态
            cv2.putText(image, "RAMP MODE: Active", (10, 180), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        else:
            # 正常模式：计算PID输出
            pid_output = self.calculate_pid_output(cx, w)
            error_value = self.Error
            
            # 更新弯道计数
            self.update_curve_count(error_value)
            
            # 设置基础速度
            linear_x = 0.15
            angular_z = float(pid_output) / 8
            
            # 在6.2米处执行一次暂停（范围检测避免浮点精度问题）
            if 2.0 <= self.distance_traveled <= 3.0:
                linear_x = 0.2
            if 6.2 <= self.distance_traveled <= 6.3: 
                rospy.loginfo("到达6.2米处，执行暂停")
                self.twist.linear.x = 0
                self.twist.angular.z = 0
                self.cmd_vel_pub.publish(self.twist)
                self.paused = True
                self.task_complete_pub.publish(4)
                
            if 6.9 <= self.distance_traveled <= 7.0: 
                rospy.loginfo("到达6.6米处，执行暂停")
                self.twist.linear.x = 0
                self.twist.angular.z = 0
                self.cmd_vel_pub.publish(self.twist)
                self.paused = True
                self.task_complete_pub.publish(5)
                
            if 7.6 <= self.distance_traveled <= 7.7: 
                rospy.loginfo("到达7.1米处，执行暂停")
                self.twist.linear.x = 0
                self.twist.angular.z = 0
                self.cmd_vel_pub.publish(self.twist)
                self.paused = True
                self.task_complete_pub.publish(6)
                
                

            
            # 设置并发布速度
            self.twist.linear.x = linear_x
            self.twist.angular.z = angular_z
            self.cmd_vel_pub.publish(self.twist)

        # 在原始图像上绘制中心点
        cv2.circle(image, (cx, cy), 20, (0, 0, 255), -1)
        
        # 显示状态信息
        cv2.putText(image, "Status: %s" % status, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        cv2.putText(image, "Distance: %.2fm" % self.distance_traveled, (10, 60), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        cv2.putText(image, "Error: %.1f" % self.Error, (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        cv2.putText(image, "Left Curves: %d" % self.left_curve_count, (10, 120), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(image, "Right Curves: %d" % self.right_curve_count, (10, 150), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 创建二值图像的彩色显示
        binary_display = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
        
        # 在二值图像上绘制搜索区域
        cv2.rectangle(binary_display, (0, search_top), (w-1, search_bot-1), (0, 0, 255), 2)
        cv2.line(binary_display, (mid_width, search_top), (mid_width, search_bot), (0, 255, 0), 1)
        
        # 在二值图像上绘制中心点
        cv2.circle(binary_display, (cx, cy), 20, (0, 0, 255), -1)
        
        # 标记质心位置
        if left_detected:
            cv2.circle(binary_display, (left_cx_global, left_cy_global), 10, (0, 255, 0), -1)
        if right_detected:
            cv2.circle(binary_display, (right_cx_global, right_cy_global), 10, (255, 0, 0), -1)
        
        # 创建组合显示
        scale = 0.5
        img1 = cv2.resize(image, (int(w*scale), int(h*scale)))
        img3 = cv2.resize(binary_display, (int(w*scale), int(h*scale)))
        
        combined = np.vstack((img1, img3))
        cv2.imshow("原始图像 | 处理后的二值图", combined)
        cv2.waitKey(3)

        # 输出调试信息
        print("距离: %.2fm, 状态: %s, 中心: %d, 误差: %.1f, 左弯: %d, 右弯: %d, 冲坡: %s" % 
              (self.distance_traveled, status, cx, self.Error, 
               self.left_curve_count, self.right_curve_count, 
               "是" if self.ramp_mode else "否"))


def main():
    rospy.init_node('follower_node', anonymous=True)
    follower = Follower()
    rospy.spin()


if __name__ == '__main__':
    try:
        main()
    except rospy.ROSInterruptException:
        pass
