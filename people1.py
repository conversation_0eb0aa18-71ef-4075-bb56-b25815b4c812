#!/usr/bin/env python3
# coding=utf-8
from ultralytics import YOLO
import cv2
import time
import os
import rospy
from std_msgs.msg import Int32
from sensor_msgs.msg import Image
import cv_bridge

# 全局任务状态标志
task_in_progress = False
task_reset_pending = False
task_count = 0  # 任务计数器
last_frame = None  # 存储最新的图像帧
bridge = cv_bridge.CvBridge()  # 创建CvBridge实例

def image_callback(msg):
    global last_frame
    try:
        # 将ROS的Image消息转换为OpenCV格式的图像
        last_frame = bridge.imgmsg_to_cv2(msg, "bgr8")
    except cv_bridge.CvBridgeError as e:
        rospy.logerr("图像转换失败: %s", e)

def capture_and_save_photo(photo_id):
    global last_frame
    
    # 确保保存图片的目录存在
    output_dir = "img"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 使用任务ID生成唯一文件名
    output_path = os.path.join(output_dir, f"task_{photo_id}_img.jpg")
    
    # 检查是否有可用的图像帧
    if last_frame is None:
        rospy.logerr("没有可用的图像帧")
        return False
    
    # 保存当前帧到指定路径
    try:
        cv2.imwrite(output_path, last_frame)
        rospy.loginfo(f"图像已保存到 {output_path}")
        return True
    except Exception as e:
        rospy.logerr(f"保存图像失败: {str(e)}")
        return False

# 加载模型
model = YOLO("best.pt")  # 加载预训练模型

def predict(photo_id):
    # 使用任务ID构建对应图像路径
    output_path = os.path.join("img", f"task_{photo_id}_img.jpg")
    
    # 在继续之前检查文件是否存在
    if not os.path.exists(output_path):
        rospy.logerr(f"错误：未找到文件 {output_path}")
        return 0, 0
    
    # 进行预测
    results = model(
      source=output_path,
      conf=0.35,
      iou=0.45,
      imgsz=640,
      half=False,
      device='cpu',
      show_labels=True,
      show=False,
      save=True,
      save_txt=False,
      save_conf=False,
      save_crop=False,
      show_conf=False,
      max_det=300,
      line_width=None,
      visualize=False,
      augment=False,
      agnostic_nms=False,
      retina_masks=False,
      classes=None,
      boxes=True,
    )

    professional = 0  # 职业人员(类别0)
    ordinary = 0      # 普通人员(类别1)，修正为从0开始计数

    for result in results:
        cls = result.boxes.cls.cpu().numpy()  # 获取分类
        
        for class_id in cls:
            if class_id == 0:  # 职业人员
                professional += 1
            elif class_id == 1:  # 普通人员
                ordinary += 1

    return professional, ordinary

def callback(data):
    global task_in_progress, task_reset_pending, task_count
    
    # 收到终止信号时重置状态
    if data.data == 1:
        task_in_progress = False
        task_reset_pending = False
        rospy.loginfo("收到重置信号，状态已重置")
        return
    
    # 收到任务信号但任务正在进行中
    if data.data == 2 and task_in_progress:
        rospy.loginfo("任务执行中，忽略信号:2")
        return
    
    # 收到新任务信号且系统空闲
    if data.data == 2 and not task_in_progress:
        rospy.loginfo("== 收到启动信号，开始任务 ==")
        task_in_progress = True
        task_count += 1  # 递增任务计数器
        
        try:
            # 阶段1: 拍照（使用任务ID）
            rospy.loginfo(f"正在执行任务#{task_count} - 拍照...")
            if not capture_and_save_photo(photo_id=task_count):
                raise Exception("拍照失败")
            
            # 阶段2: 识别（使用任务ID）
            rospy.loginfo(f"正在执行任务#{task_count} - 识别...")
            professional, ordinary = predict(photo_id=task_count)
            
            # 阶段3: 输出结果
            rospy.loginfo(f"任务#{task_count} 识别完成：职业人员={professional}, 普通人员={ordinary}")
            
        except Exception as e:
            rospy.logerr(f"任务执行出错: {str(e)}")
        finally:
            # 任务完成标记
            rospy.loginfo("== 任务执行完成 ==")
            task_reset_pending = True  # 准备重置状态

def reset_task_state():
    global task_in_progress, task_reset_pending
    if task_reset_pending:
        task_in_progress = False
        task_reset_pending = False
        rospy.loginfo("系统状态已重置，等待新任务")

def subscriber_node():
    global task_in_progress, task_reset_pending
    
    # 初始化ROS节点
    rospy.init_node('number_subscriber', anonymous=True)

    # 创建一个订阅者
    rospy.Subscriber('number_topic', Int32, callback, queue_size=10)
    
    # 订阅图像话题
    rospy.Subscriber('/camera/color/image_raw', Image, image_callback, queue_size=1)
    
    # 创建定时器定期重置状态
    rospy.Timer(rospy.Duration(0.5), lambda event: reset_task_state())
    
    rospy.loginfo("就绪：等待启动信号(2)或重置信号(1)")
    rospy.spin()

if __name__ == "__main__":
    subscriber_node()
